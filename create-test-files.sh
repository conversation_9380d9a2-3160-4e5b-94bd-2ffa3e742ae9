#!/bin/bash

# Base directory where test files will be created
BASE_DIR="./ericsson/gnodeb/xml3gpp_n19q2/pm1"

# Create the directory structure
for ENM in ERCS_ENM1 ERCS_ENM2 ERCS_ENM3 ERCS_ENM4 ERCS_ENM5; do
  mkdir -p "$BASE_DIR/$ENM"
  
  # Create 3 subdirectories in each ENM folder
  for i in {1..3}; do
    SUBDIR="NODE$i"
    mkdir -p "$BASE_DIR/$ENM/$SUBDIR"
    
    # Create a test file with the pattern from the script
    touch "$BASE_DIR/$ENM/$SUBDIR/A20250608.0400+0100-0415+0100_${SUBDIR}_statsfile.xml.gz"
    
    echo "Created: $BASE_DIR/$ENM/$SUBDIR/A20250608.0400+0100-0415+0100_${SUBDIR}_statsfile.xml.gz"
  done
done

# Create the output directory
mkdir -p "/tmp/muniaraj"

echo "Test files created successfully!"
echo "You can now run draft-script-muni.sh to test with these files."
#!/bin/bash
 
BASE_DIR="/mnt/c/Users/<USER>/Documents/augment-projects/testbed/ericsson/gnodeb/xml3gpp_n19q2/pm1"               # <-- Change as needed
OUTPUT_LIST_DIR="/tmp/muniaraj"                  # List files go here
TAR_OUTPUT_DIR="/mnt/c/Users/<USER>/Documents/augment-projects/testbed"  # Tarballs go here
LOG_FILE="./tmp/tar_creation.log"        # All logs go here
PARALLEL_LIMIT=3
TAR_DATE="A20250608-0400-0415_Hrs"
TAR_TIME_PATTERN="A20250608.0400+0100-0415+0100"
 
mkdir -p "$TAR_OUTPUT_DIR"
> "$LOG_FILE"  # Clear log file
 
cd "$BASE_DIR" || exit 1
 
echo "?? Generating file lists..." | tee -a "$LOG_FILE"
for OMC in $(ls -1 | grep ^ERCS_ENM1); do
  if [ -d "$OMC" ]; then
    out_file="${OUTPUT_LIST_DIR}/${OMC}-${TAR_DATE}.txt"
> "$out_file"
    for i in $(ls "$OMC"); do
      echo "$BASE_DIR/$OMC/$i/${TAR_TIME_PATTERN}_${i}_statsfile.xml.gz" >> "$out_file"
    done
    echo "  ? Created list: $out_file" | tee -a "$LOG_FILE"
  fi
done
 
echo -e "\n?? Creating tarballs in parallel with % progress and logging...\n" | tee -a "$LOG_FILE"
 
job_count=0
 
for list_file in "$OUTPUT_LIST_DIR"/ERCS_ENM1-${TAR_DATE}.txt; do
  base_name=$(basename "$list_file" .txt)
  output_tar="${TAR_OUTPUT_DIR}/${base_name}.tar"
  total_files=$(wc -l < "$list_file")
 
  (
    #echo "? Starting tar: $output_tar (Total files: $total_files)" | tee -a "$LOG_FILE"
    SECONDS=0
    count=0
    tar -cf "$output_tar" -T "$list_file"
    echo "[$(date '+%H:%M:%S')] $output_tar: 100% complete ($total_files/$total_files)" | tee -a "$LOG_FILE"
    echo "? Done: $output_tar (Time taken: ${SECONDS}s)" | tee -a "$LOG_FILE"  ) &
 
  ((job_count++))
  if (( job_count % PARALLEL_LIMIT == 0 )); then
    wait
  fi
done
 
wait
echo -e "\n? All tarballs created. Full log: $LOG_FILE"